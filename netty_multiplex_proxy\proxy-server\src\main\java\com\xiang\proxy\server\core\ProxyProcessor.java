package com.xiang.proxy.server.core;

import com.xiang.proxy.server.config.ProxyProcessorConfig;
import com.xiang.proxy.server.inbound.InboundHandler;
import com.xiang.proxy.server.router.Router;
import com.xiang.proxy.server.router.RouteResult;
import com.xiang.proxy.server.outbound.OutboundHandler;
import com.xiang.proxy.server.outbound.OutboundConnection;
import io.netty.buffer.ByteBuf;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 代理处理器核心类
 * 整合Inbound、Router、Outbound三个组件，实现完整的代理流程
 * 使用多队列和多线程处理模式提高并发性能
 */
public class ProxyProcessor {
    private static final Logger logger = LoggerFactory.getLogger(ProxyProcessor.class);
    private static final String OUTBOUND_CONNECTION_ATTR = "outbound.connection";
    private static final String OUTBOUND_HANDLER_ATTR = "outbound.handler";

    private final Router router;
    private final ProxyProcessorConfig config;
    private final Map<String, OutboundHandler> outboundHandlers;
    private final Map<String, InboundHandler> inboundHandlers;
    private final Map<String, OutboundConnection> activeConnections;

    // 队列相关
    private final int queueCount;
    private final BlockingQueue<QueuedRequest>[] requestQueues;
    private final ExecutorService workerExecutors;
    private final AtomicBoolean running;
    private final AtomicInteger requestCounter;
    private int threadIndex = 0;

    // 性能监控和自适应管理
    private final ProxyMetrics metrics;
    private final AdaptiveQueueManager adaptiveManager;

    public ProxyProcessor(Router router) {
        this(router, ProxyProcessorConfig.defaultConfig());
    }

    public ProxyProcessor(Router router, int queueCount) {
        this(router, new ProxyProcessorConfig(queueCount, ProxyProcessorConfig.DEFAULT_QUEUE_CAPACITY));
    }

    @SuppressWarnings("unchecked")
    public ProxyProcessor(Router router, ProxyProcessorConfig config) {
        this.router = router;
        this.config = config;
        this.queueCount = config.getQueueCount();
        this.outboundHandlers = new ConcurrentHashMap<>();
        this.inboundHandlers = new ConcurrentHashMap<>();
        this.activeConnections = new ConcurrentHashMap<>();
        this.running = new AtomicBoolean(false);
        this.requestCounter = new AtomicInteger(0);

        // 初始化性能监控
        this.metrics = new ProxyMetrics();

        // 初始化自适应管理器（如果启用）
        if (config.isEnableAdaptiveAdjustment()) {
            this.adaptiveManager = new AdaptiveQueueManager(this, metrics);
            logger.info("自适应队列管理器已启用");
        } else {
            this.adaptiveManager = null;
            logger.info("自适应队列管理器已禁用");
        }

        // 初始化队列
        this.requestQueues = new BlockingQueue[queueCount];
        this.workerExecutors = Executors.newFixedThreadPool(queueCount, r -> {
            Thread t = new Thread(r, config.getWorkerThreadPrefix() + threadIndex++);
            t.setDaemon(true);
            return t;
        });

        for (int i = 0; i < queueCount; i++) {
            this.requestQueues[i] = new LinkedBlockingQueue<>(config.getQueueCapacity());
        }

        logger.info("ProxyProcessor初始化完成: {}", config);
    }

    /**
     * 启动处理器
     */
    public void start() {
        if (running.compareAndSet(false, true)) {
            logger.info("启动ProxyProcessor工作线程...");
            for (int i = 0; i < queueCount; i++) {
                final int queueIndex = i;
                workerExecutors.submit(() -> processQueue(queueIndex));
            }

            // 启动自适应管理器
            if (adaptiveManager != null) {
                adaptiveManager.start();
                logger.info("自适应队列管理器已启动");
            }

            logger.info("ProxyProcessor启动完成，工作线程数: {}", queueCount);
        }
    }

    /**
     * 队列处理任务
     */
    protected void processQueue(int queueIndex) {
        logger.debug("队列 {} 工作线程启动", queueIndex);
        BlockingQueue<QueuedRequest> queue = requestQueues[queueIndex];

        while (running.get()) {
            try {
                QueuedRequest queuedRequest = queue.poll(1, TimeUnit.SECONDS);
                if (queuedRequest != null) {
                    processQueuedRequest(queuedRequest, queueIndex);
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                logger.info("队列 {} 工作线程被中断", queueIndex);
                break;
            } catch (Exception e) {
                logger.error("队列 {} 处理请求时发生异常", queueIndex, e);
            }
        }

        logger.debug("队列 {} 工作线程结束", queueIndex);
    }

    /**
     * 处理队列中的请求
     */
    protected void processQueuedRequest(QueuedRequest queuedRequest, int queueIndex) {
        ProxyRequest request = queuedRequest.getRequest();
        CompletableFuture<ProxyResponse> future = queuedRequest.getFuture();

        // 开始性能计时
        ProxyMetrics.RequestTimer timer = metrics.startRequest();

        // 记录队列等待时间
        long waitTime = System.currentTimeMillis() - queuedRequest.getQueueTime();
        metrics.recordQueueMetrics(queueIndex, getRequestQueue(queueIndex).size(), waitTime);

        try {
            logger.debug("队列 {} 开始处理请求: {}", queueIndex, request.getRequestId());

            // 检查是否是直接数据发送请求（来自MultiplexSession的handleDataPacket）
            ByteBuf directData = request.getData();
            OutboundConnection directConnection = request.getAttribute(OUTBOUND_CONNECTION_ATTR);
            OutboundHandler directHandler = request.getAttribute(OUTBOUND_HANDLER_ATTR);

            if (directConnection != null && directHandler != null) {
                // 直接发送数据，无需路由和建立连接
                directHandler.sendData(directConnection, directData)
                        .whenComplete((result, throwable) -> {
                            metrics.recordSuccess(timer); // 记录成功指标

                            if (throwable != null) {
                                logger.error("队列 {} 直接发送数据失败: {}", queueIndex, request.getRequestId(), throwable);
                                future.complete(ProxyResponse.error(request.getRequestId(), throwable.getMessage()));
                            } else {
                                logger.debug("队列 {} 直接发送数据成功: {}", queueIndex, request.getRequestId());
                                future.complete(ProxyResponse.success(request.getRequestId(), directConnection));
                            }
                        });
                return;
            }

            // 执行路由
            RouteResult routeResult = routeRequest(request);
            if (!routeResult.isSuccess()) {
                metrics.recordFailure(timer);
                future.complete(ProxyResponse.failure(request.getRequestId(), routeResult.getReason()));
                return;
            }

            // 处理Outbound
            handleOutbound(request, routeResult)
                    .whenComplete((response, throwable) -> {
                        if (throwable != null) {
                            logger.error("队列 {} 处理请求失败: {}", queueIndex, request.getRequestId(), throwable);
                            future.complete(ProxyResponse.error(request.getRequestId(), throwable.getMessage()));
                        } else {
                            logger.debug("队列 {} 处理请求成功: {}", queueIndex, request.getRequestId());
                            future.complete(response);
                        }
                    });

        } catch (Exception e) {
            metrics.recordFailure(timer);
            logger.error("队列 {} 处理请求时发生异常: {}", queueIndex, request.getRequestId(), e);
            future.complete(ProxyResponse.error(request.getRequestId(), e.getMessage()));
        }
    }

    /**
     * 计算请求应该分配到哪个队列
     * 使用改进的哈希算法确保相同连接的请求路由到同一队列
     */
    private int calculateQueueIndex(ProxyRequest request) {
        // 构建连接键值，确保格式一致性
        StringBuilder keyBuilder = new StringBuilder();
        keyBuilder.append(request.getTargetHost()).append(":")
                  .append(request.getTargetPort()).append(":")
                  .append(request.getProtocol()).append(":")
                  .append(request.getClientId());

        String connectionKey = keyBuilder.toString();

        // 使用改进的哈希算法，避免负数问题
        int hash = connectionKey.hashCode();
        // 使用位运算确保结果为正数，避免Integer.MIN_VALUE的问题
        int positiveHash = hash & 0x7FFFFFFF;
        int queueIndex = positiveHash % queueCount;

        logger.debug("请求路由到队列: connectionKey={}, hash={}, queueIndex={}",
                    connectionKey, hash, queueIndex);

        return queueIndex;
    }

    /**
     * 队列请求包装类
     */
    protected static class QueuedRequest {
        private final ProxyRequest request;
        private final CompletableFuture<ProxyResponse> future;
        private final long queueTime;

        public QueuedRequest(ProxyRequest request, CompletableFuture<ProxyResponse> future) {
            this.request = request;
            this.future = future;
            this.queueTime = System.currentTimeMillis();
        }

        public ProxyRequest getRequest() {
            return request;
        }

        public CompletableFuture<ProxyResponse> getFuture() {
            return future;
        }

        public long getQueueTime() {
            return queueTime;
        }
    }

    /**
     * 获取队列统计信息
     */
    public QueueStats getQueueStats() {
        int[] queueSizes = new int[queueCount];
        int totalSize = 0;

        for (int i = 0; i < queueCount; i++) {
            queueSizes[i] = requestQueues[i].size();
            totalSize += queueSizes[i];
        }

        return new QueueStats(queueCount, queueSizes, totalSize, requestCounter.get());
    }

    /**
     * 队列统计信息类
     */
    public static class QueueStats {
        private final int queueCount;
        private final int[] queueSizes;
        private final int totalQueueSize;
        private final int processedRequests;

        public QueueStats(int queueCount, int[] queueSizes, int totalQueueSize, int processedRequests) {
            this.queueCount = queueCount;
            this.queueSizes = queueSizes.clone();
            this.totalQueueSize = totalQueueSize;
            this.processedRequests = processedRequests;
        }

        public int getQueueCount() {
            return queueCount;
        }

        public int[] getQueueSizes() {
            return queueSizes.clone();
        }

        public int getTotalQueueSize() {
            return totalQueueSize;
        }

        public int getProcessedRequests() {
            return processedRequests;
        }

        @Override
        public String toString() {
            return String.format("QueueStats{queueCount=%d, totalSize=%d, processed=%d}",
                    queueCount, totalQueueSize, processedRequests);
        }
    }

    /**
     * 处理代理请求的主要方法 - 异步队列模式
     */
    public CompletableFuture<ProxyResponse> processRequest(ProxyRequest request) {
        if (!running.get()) {
            return CompletableFuture.completedFuture(
                    ProxyResponse.failure(request.getRequestId(), "ProxyProcessor is not running"));
        }

        logger.debug("接收代理请求: {}", request);

        // 计算队列索引
        int queueIndex = calculateQueueIndex(request);
        BlockingQueue<QueuedRequest> queue = requestQueues[queueIndex];

        // 创建Future用于异步返回结果
        CompletableFuture<ProxyResponse> future = new CompletableFuture<>();
        QueuedRequest queuedRequest = new QueuedRequest(request, future);

        // 尝试将请求加入队列
        if (queue.offer(queuedRequest)) {
            requestCounter.incrementAndGet();
            logger.debug("请求 {} 已加入队列 {}, 当前队列大小: {}",
                    request.getRequestId(), queueIndex, queue.size());
        } else {
            // 队列满了，直接返回失败
            logger.warn("队列 {} 已满，拒绝请求: {}", queueIndex, request.getRequestId());
            metrics.recordRejection();
            future.complete(ProxyResponse.failure(request.getRequestId(),
                    "Request queue is full, queue index: " + queueIndex));
        }

        return future;
    }

    /**
     * 路由请求
     */
    private RouteResult routeRequest(ProxyRequest request) {
        try {
            // 添加路由属性到请求中
            request.getAttributes().put(ProxyRequest.Attributes.ROUTE_RULE_ID, "");

            RouteResult result = router.route(request);

            if (result.isSuccess()) {
                // 将路由信息添加到请求属性中
                request.getAttributes().put(ProxyRequest.Attributes.ROUTE_RULE_ID, result.getRuleId());
                request.getAttributes().put(ProxyRequest.Attributes.OUTBOUND_ID, result.getOutboundId());

                logger.debug("请求 {} 路由成功: outbound={}, rule={}",
                        request.getRequestId(), result.getOutboundId(), result.getRuleId());
            } else {
                logger.warn("请求 {} 路由失败: {}", request.getRequestId(), result.getReason());
            }

            return result;
        } catch (Exception e) {
            logger.error("路由请求时发生异常: {}", request.getRequestId(), e);
            return RouteResult.error("Route error: " + e.getMessage());
        }
    }

    /**
     * 处理Outbound连接和数据转发
     */
    private CompletableFuture<ProxyResponse> handleOutbound(ProxyRequest request, RouteResult routeResult) {
        String outboundId = routeResult.getOutboundId();
        OutboundHandler outboundHandler = outboundHandlers.get(outboundId);

        if (outboundHandler == null) {
            String error = "Outbound handler not found: " + outboundId;
            logger.error("请求 {} 处理失败: {}", request.getRequestId(), error);
            return CompletableFuture.completedFuture(ProxyResponse.failure(request.getRequestId(), error));
        }

        // 建立Outbound连接
        return outboundHandler.connect(request)
                .thenCompose(connection -> {
                    // 保存活跃连接
                    activeConnections.put(connection.getConnectionId(), connection);

                    logger.debug("请求 {} Outbound连接建立成功: {}",
                            request.getRequestId(), connection.getConnectionId());

                    // 如果请求包含数据，立即发送
                    if (request.hasData()) {
                        return sendDataToOutbound(request, connection)
                                .thenApply(v -> ProxyResponse.success(request.getRequestId(), connection));
                    } else {
                        return CompletableFuture.completedFuture(
                                ProxyResponse.success(request.getRequestId(), connection));
                    }
                })
                .exceptionally(throwable -> {
                    logger.error("请求 {} Outbound处理失败: {}", request.getRequestId(), throwable.getMessage());
                    return ProxyResponse.error(request.getRequestId(), throwable.getMessage());
                });
    }

    /**
     * 发送数据到Outbound
     */
    public CompletableFuture<Void> sendDataToOutbound(ProxyRequest request, OutboundConnection connection) {
        String outboundId = connection.getAttribute(OutboundConnection.Attributes.OUTBOUND_ID);
        OutboundHandler outboundHandler = outboundHandlers.get(outboundId);

        if (outboundHandler == null) {
            return CompletableFuture.failedFuture(
                    new IllegalStateException("Outbound handler not found: " + outboundId));
        }

        return outboundHandler.sendData(connection, request.getData())
                .whenComplete((result, throwable) -> {
                    if (throwable != null) {
                        logger.error("发送数据到Outbound失败: connection={}, error={}",
                                connection.getConnectionId(), throwable.getMessage());
                    } else {
                        logger.debug("发送数据到Outbound成功: connection={}, bytes={}",
                                connection.getConnectionId(), request.getData().readableBytes());
                    }
                });
    }

    /**
     * 关闭连接
     */
    public CompletableFuture<Void> closeConnection(String connectionId) {
        OutboundConnection connection = activeConnections.remove(connectionId);
        if (connection == null) {
            return CompletableFuture.completedFuture(null);
        }

        String outboundId = connection.getAttribute(OutboundConnection.Attributes.OUTBOUND_ID);
        OutboundHandler outboundHandler = outboundHandlers.get(outboundId);

        if (outboundHandler == null) {
            logger.warn("关闭连接时未找到Outbound处理器: {}", outboundId);
            return CompletableFuture.completedFuture(null);
        }

        return outboundHandler.closeConnection(connection)
                .whenComplete((result, throwable) -> {
                    if (throwable != null) {
                        logger.warn("关闭连接失败: {}", connectionId, throwable);
                    } else {
                        logger.debug("连接关闭成功: {}", connectionId);
                    }
                });
    }

    /**
     * 注册Outbound处理器
     */
    public void registerOutboundHandler(OutboundHandler handler) {
        outboundHandlers.put(handler.getOutboundId(), handler);
        logger.info("注册Outbound处理器: {} ({})", handler.getOutboundId(), handler.getType());
    }

    /**
     * 注销Outbound处理器
     */
    public void unregisterOutboundHandler(String outboundId) {
        OutboundHandler removed = outboundHandlers.remove(outboundId);
        if (removed != null) {
            logger.info("注销Outbound处理器: {}", outboundId);
        }
    }

    /**
     * 注册Inbound处理器
     */
    public void registerInboundHandler(InboundHandler handler) {
        inboundHandlers.put(handler.getProtocolName(), handler);
        logger.info("注册Inbound处理器: {} (优先级: {})",
                handler.getProtocolName(), handler.getPriority());
    }

    /**
     * 注销Inbound处理器
     */
    public void unregisterInboundHandler(String protocolName) {
        InboundHandler removed = inboundHandlers.remove(protocolName);
        if (removed != null) {
            logger.info("注销Inbound处理器: {}", protocolName);
        }
    }

    /**
     * 获取活跃连接数
     */
    public int getActiveConnectionCount() {
        return activeConnections.size();
    }

    /**
     * 获取活跃连接
     */
    public OutboundConnection getActiveConnection(String connectionId) {
        return activeConnections.get(connectionId);
    }

    /**
     * 获取Outbound处理器
     */
    public OutboundHandler getOutboundHandler(String outboundId) {
        return outboundHandlers.get(outboundId);
    }

    /**
     * 获取Inbound处理器
     */
    public InboundHandler getInboundHandler(String protocolName) {
        return inboundHandlers.get(protocolName);
    }

    /**
     * 获取路由器
     */
    public Router getRouter() {
        return router;
    }

    /**
     * 获取配置信息
     */
    public ProxyProcessorConfig getConfig() {
        return config;
    }

    /**
     * 获取队列数量
     */
    public int getQueueCount() {
        return queueCount;
    }

    /**
     * 检查处理器是否正在运行
     */
    public boolean isRunning() {
        return running.get();
    }

    /**
     * 获取请求队列 - 供子类访问
     */
    protected BlockingQueue<QueuedRequest> getRequestQueue(int index) {
        if (index < 0 || index >= queueCount) {
            throw new IllegalArgumentException("Invalid queue index: " + index);
        }
        return requestQueues[index];
    }

    /**
     * 获取运行状态 - 供子类访问
     */
    protected AtomicBoolean getRunningState() {
        return running;
    }

    /**
     * 获取性能指标
     */
    public ProxyMetrics getMetrics() {
        return metrics;
    }

    /**
     * 获取自适应队列管理器
     */
    public AdaptiveQueueManager getAdaptiveManager() {
        return adaptiveManager;
    }

    /**
     * 获取性能报告
     */
    public ProxyMetrics.MetricsReport getMetricsReport() {
        return metrics.getReport();
    }

    /**
     * 获取调整建议
     */
    public String getAdjustmentRecommendations() {
        if (adaptiveManager != null) {
            return adaptiveManager.getAdjustmentRecommendations();
        }
        return "自适应管理器未启用";
    }

    /**
     * 关闭处理器，清理资源
     */
    public void shutdown() {
        logger.info("开始关闭代理处理器...");

        // 停止接收新请求
        running.set(false);

        // 停止自适应管理器
        if (adaptiveManager != null) {
            adaptiveManager.stop();
            logger.info("自适应队列管理器已停止");
        }

        // 等待队列中的请求处理完成
        logger.info("等待队列中的请求处理完成...");
        for (int i = 0; i < queueCount; i++) {
            BlockingQueue<QueuedRequest> queue = requestQueues[i];
            int remainingRequests = queue.size();
            if (remainingRequests > 0) {
                logger.info("队列 {} 还有 {} 个待处理请求", i, remainingRequests);

                // 取消剩余请求
                QueuedRequest queuedRequest;
                while ((queuedRequest = queue.poll()) != null) {
                    queuedRequest.getFuture().complete(
                            ProxyResponse.failure(queuedRequest.getRequest().getRequestId(),
                                    "ProxyProcessor is shutting down"));
                }
            }
        }

        // 关闭工作线程
        logger.info("关闭工作线程...");
        workerExecutors.shutdown();
        try {
            if (!workerExecutors.awaitTermination(config.getShutdownTimeoutSeconds(), TimeUnit.SECONDS)) {
                logger.warn("工作线程池未能在{}秒内正常关闭，强制关闭", config.getShutdownTimeoutSeconds());
                workerExecutors.shutdownNow();
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            workerExecutors.shutdownNow();
        }

        // 关闭所有活跃连接
        logger.info("关闭活跃连接...");
        activeConnections.values().forEach(connection -> {
            try {
                closeConnection(connection.getConnectionId()).get(3, TimeUnit.SECONDS);
            } catch (Exception e) {
                logger.warn("关闭连接时发生异常: {}", connection.getConnectionId(), e);
            }
        });

        // 销毁所有处理器
        logger.info("销毁处理器...");
        outboundHandlers.values().forEach(handler -> {
            try {
                handler.destroy();
            } catch (Exception e) {
                logger.warn("销毁Outbound处理器时发生异常: {}", handler.getOutboundId(), e);
            }
        });

        inboundHandlers.values().forEach(handler -> {
            try {
                handler.destroy();
            } catch (Exception e) {
                logger.warn("销毁Inbound处理器时发生异常: {}", handler.getProtocolName(), e);
            }
        });

        // 清理资源
        activeConnections.clear();
        outboundHandlers.clear();
        inboundHandlers.clear();

        logger.info("代理处理器关闭完成，总处理请求数: {}", requestCounter.get());
    }
}