<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report-3.0.xsd" version="3.0" name="com.xiang.proxy.server.core.ProxyProcessorQueueTest" time="0.328" tests="5" errors="0" skipped="0" failures="0">
  <properties>
    <property name="java.specification.version" value="21"/>
    <property name="sun.cpu.isalist" value="amd64"/>
    <property name="sun.jnu.encoding" value="GBK"/>
    <property name="java.class.path" value="C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\target\test-classes;C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\target\classes;E:\.m2\repository\io\netty\netty-all\4.1.100.Final\netty-all-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-buffer\4.1.100.Final\netty-buffer-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-codec\4.1.100.Final\netty-codec-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-codec-dns\4.1.100.Final\netty-codec-dns-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-codec-haproxy\4.1.100.Final\netty-codec-haproxy-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-codec-http\4.1.100.Final\netty-codec-http-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-codec-http2\4.1.100.Final\netty-codec-http2-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-codec-memcache\4.1.100.Final\netty-codec-memcache-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-codec-mqtt\4.1.100.Final\netty-codec-mqtt-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-codec-redis\4.1.100.Final\netty-codec-redis-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-codec-smtp\4.1.100.Final\netty-codec-smtp-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-codec-socks\4.1.100.Final\netty-codec-socks-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-codec-stomp\4.1.100.Final\netty-codec-stomp-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-codec-xml\4.1.100.Final\netty-codec-xml-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-common\4.1.100.Final\netty-common-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-handler\4.1.100.Final\netty-handler-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-transport-native-unix-common\4.1.100.Final\netty-transport-native-unix-common-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-handler-proxy\4.1.100.Final\netty-handler-proxy-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-handler-ssl-ocsp\4.1.100.Final\netty-handler-ssl-ocsp-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-resolver\4.1.100.Final\netty-resolver-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-resolver-dns\4.1.100.Final\netty-resolver-dns-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-transport\4.1.100.Final\netty-transport-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-transport-rxtx\4.1.100.Final\netty-transport-rxtx-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-transport-sctp\4.1.100.Final\netty-transport-sctp-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-transport-udt\4.1.100.Final\netty-transport-udt-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-transport-classes-epoll\4.1.100.Final\netty-transport-classes-epoll-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-transport-classes-kqueue\4.1.100.Final\netty-transport-classes-kqueue-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-resolver-dns-classes-macos\4.1.100.Final\netty-resolver-dns-classes-macos-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-transport-native-epoll\4.1.100.Final\netty-transport-native-epoll-4.1.100.Final-linux-x86_64.jar;E:\.m2\repository\io\netty\netty-transport-native-epoll\4.1.100.Final\netty-transport-native-epoll-4.1.100.Final-linux-aarch_64.jar;E:\.m2\repository\io\netty\netty-transport-native-kqueue\4.1.100.Final\netty-transport-native-kqueue-4.1.100.Final-osx-x86_64.jar;E:\.m2\repository\io\netty\netty-transport-native-kqueue\4.1.100.Final\netty-transport-native-kqueue-4.1.100.Final-osx-aarch_64.jar;E:\.m2\repository\io\netty\netty-resolver-dns-native-macos\4.1.100.Final\netty-resolver-dns-native-macos-4.1.100.Final-osx-x86_64.jar;E:\.m2\repository\io\netty\netty-resolver-dns-native-macos\4.1.100.Final\netty-resolver-dns-native-macos-4.1.100.Final-osx-aarch_64.jar;E:\.m2\repository\org\slf4j\slf4j-api\1.7.36\slf4j-api-1.7.36.jar;E:\.m2\repository\ch\qos\logback\logback-classic\1.2.12\logback-classic-1.2.12.jar;E:\.m2\repository\ch\qos\logback\logback-core\1.2.12\logback-core-1.2.12.jar;E:\.m2\repository\org\yaml\snakeyaml\2.2\snakeyaml-2.2.jar;E:\.m2\repository\org\junit\jupiter\junit-jupiter-api\5.10.0\junit-jupiter-api-5.10.0.jar;E:\.m2\repository\org\opentest4j\opentest4j\1.3.0\opentest4j-1.3.0.jar;E:\.m2\repository\org\junit\platform\junit-platform-commons\1.10.0\junit-platform-commons-1.10.0.jar;E:\.m2\repository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;E:\.m2\repository\org\junit\jupiter\junit-jupiter-engine\5.10.0\junit-jupiter-engine-5.10.0.jar;E:\.m2\repository\org\junit\platform\junit-platform-engine\1.10.0\junit-platform-engine-1.10.0.jar;E:\.m2\repository\org\junit\jupiter\junit-jupiter-params\5.10.0\junit-jupiter-params-5.10.0.jar;E:\.m2\repository\org\mockito\mockito-core\5.5.0\mockito-core-5.5.0.jar;E:\.m2\repository\net\bytebuddy\byte-buddy\1.14.6\byte-buddy-1.14.6.jar;E:\.m2\repository\net\bytebuddy\byte-buddy-agent\1.14.6\byte-buddy-agent-1.14.6.jar;E:\.m2\repository\org\objenesis\objenesis\3.3\objenesis-3.3.jar;E:\.m2\repository\org\mockito\mockito-junit-jupiter\5.5.0\mockito-junit-jupiter-5.5.0.jar;"/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="user.variant" value=""/>
    <property name="java.vendor.url" value="https://java.oracle.com/"/>
    <property name="user.timezone" value="Asia/Shanghai"/>
    <property name="os.name" value="Windows 11"/>
    <property name="java.vm.specification.version" value="21"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="user.country" value="CN"/>
    <property name="sun.boot.library.path" value="C:\Program Files\Java\graalvm-jdk-21.0.8+12.1\bin"/>
    <property name="sun.java.command" value="C:\Users\<USER>\AppData\Local\Temp\surefire7856176922315187681\surefirebooter-20250822093324459_3.jar C:\Users\<USER>\AppData\Local\Temp\surefire7856176922315187681 2025-08-22T09-33-24_332-jvmRun1 surefire-20250822093324459_1tmp surefire_0-20250822093324459_2tmp"/>
    <property name="jdk.debug" value="release"/>
    <property name="test" value="ProxyProcessorQueueTest"/>
    <property name="surefire.test.class.path" value="C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\target\test-classes;C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server\target\classes;E:\.m2\repository\io\netty\netty-all\4.1.100.Final\netty-all-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-buffer\4.1.100.Final\netty-buffer-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-codec\4.1.100.Final\netty-codec-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-codec-dns\4.1.100.Final\netty-codec-dns-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-codec-haproxy\4.1.100.Final\netty-codec-haproxy-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-codec-http\4.1.100.Final\netty-codec-http-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-codec-http2\4.1.100.Final\netty-codec-http2-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-codec-memcache\4.1.100.Final\netty-codec-memcache-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-codec-mqtt\4.1.100.Final\netty-codec-mqtt-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-codec-redis\4.1.100.Final\netty-codec-redis-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-codec-smtp\4.1.100.Final\netty-codec-smtp-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-codec-socks\4.1.100.Final\netty-codec-socks-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-codec-stomp\4.1.100.Final\netty-codec-stomp-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-codec-xml\4.1.100.Final\netty-codec-xml-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-common\4.1.100.Final\netty-common-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-handler\4.1.100.Final\netty-handler-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-transport-native-unix-common\4.1.100.Final\netty-transport-native-unix-common-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-handler-proxy\4.1.100.Final\netty-handler-proxy-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-handler-ssl-ocsp\4.1.100.Final\netty-handler-ssl-ocsp-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-resolver\4.1.100.Final\netty-resolver-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-resolver-dns\4.1.100.Final\netty-resolver-dns-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-transport\4.1.100.Final\netty-transport-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-transport-rxtx\4.1.100.Final\netty-transport-rxtx-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-transport-sctp\4.1.100.Final\netty-transport-sctp-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-transport-udt\4.1.100.Final\netty-transport-udt-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-transport-classes-epoll\4.1.100.Final\netty-transport-classes-epoll-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-transport-classes-kqueue\4.1.100.Final\netty-transport-classes-kqueue-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-resolver-dns-classes-macos\4.1.100.Final\netty-resolver-dns-classes-macos-4.1.100.Final.jar;E:\.m2\repository\io\netty\netty-transport-native-epoll\4.1.100.Final\netty-transport-native-epoll-4.1.100.Final-linux-x86_64.jar;E:\.m2\repository\io\netty\netty-transport-native-epoll\4.1.100.Final\netty-transport-native-epoll-4.1.100.Final-linux-aarch_64.jar;E:\.m2\repository\io\netty\netty-transport-native-kqueue\4.1.100.Final\netty-transport-native-kqueue-4.1.100.Final-osx-x86_64.jar;E:\.m2\repository\io\netty\netty-transport-native-kqueue\4.1.100.Final\netty-transport-native-kqueue-4.1.100.Final-osx-aarch_64.jar;E:\.m2\repository\io\netty\netty-resolver-dns-native-macos\4.1.100.Final\netty-resolver-dns-native-macos-4.1.100.Final-osx-x86_64.jar;E:\.m2\repository\io\netty\netty-resolver-dns-native-macos\4.1.100.Final\netty-resolver-dns-native-macos-4.1.100.Final-osx-aarch_64.jar;E:\.m2\repository\org\slf4j\slf4j-api\1.7.36\slf4j-api-1.7.36.jar;E:\.m2\repository\ch\qos\logback\logback-classic\1.2.12\logback-classic-1.2.12.jar;E:\.m2\repository\ch\qos\logback\logback-core\1.2.12\logback-core-1.2.12.jar;E:\.m2\repository\org\yaml\snakeyaml\2.2\snakeyaml-2.2.jar;E:\.m2\repository\org\junit\jupiter\junit-jupiter-api\5.10.0\junit-jupiter-api-5.10.0.jar;E:\.m2\repository\org\opentest4j\opentest4j\1.3.0\opentest4j-1.3.0.jar;E:\.m2\repository\org\junit\platform\junit-platform-commons\1.10.0\junit-platform-commons-1.10.0.jar;E:\.m2\repository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;E:\.m2\repository\org\junit\jupiter\junit-jupiter-engine\5.10.0\junit-jupiter-engine-5.10.0.jar;E:\.m2\repository\org\junit\platform\junit-platform-engine\1.10.0\junit-platform-engine-1.10.0.jar;E:\.m2\repository\org\junit\jupiter\junit-jupiter-params\5.10.0\junit-jupiter-params-5.10.0.jar;E:\.m2\repository\org\mockito\mockito-core\5.5.0\mockito-core-5.5.0.jar;E:\.m2\repository\net\bytebuddy\byte-buddy\1.14.6\byte-buddy-1.14.6.jar;E:\.m2\repository\net\bytebuddy\byte-buddy-agent\1.14.6\byte-buddy-agent-1.14.6.jar;E:\.m2\repository\org\objenesis\objenesis\3.3\objenesis-3.3.jar;E:\.m2\repository\org\mockito\mockito-junit-jupiter\5.5.0\mockito-junit-jupiter-5.5.0.jar;"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="C:\Users\<USER>\Program Files\Java\graalvm-jdk-21.0.8+12.1"/>
    <property name="file.separator" value="\"/>
    <property name="basedir" value="C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server"/>
    <property name="java.vm.compressedOopsMode" value="Zero based"/>
    <property name="jdk.internal.vm.ci.enabled" value="true"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="surefire.real.class.path" value="C:\Users\<USER>\AppData\Local\Temp\surefire7856176922315187681\surefirebooter-20250822093324459_3.jar"/>
    <property name="user.script" value=""/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="java.runtime.version" value="21.0.8+12-LTS-jvmci-23.1-b72"/>
    <property name="user.name" value="xiang"/>
    <property name="stdout.encoding" value="GBK"/>
    <property name="path.separator" value=";"/>
    <property name="os.version" value="10.0"/>
    <property name="java.runtime.name" value="Java(TM) SE Runtime Environment"/>
    <property name="file.encoding" value="UTF-8"/>
    <property name="java.vm.name" value="Java HotSpot(TM) 64-Bit Server VM"/>
    <property name="java.vendor.version" value="Oracle GraalVM 21.0.8+12.1"/>
    <property name="localRepository" value="E:\.m2\repository"/>
    <property name="java.vendor.url.bug" value="https://bugreport.java.com/bugreport/"/>
    <property name="java.io.tmpdir" value="C:\Users\<USER>\AppData\Local\Temp\"/>
    <property name="java.version" value="21.0.8"/>
    <property name="user.dir" value="C:\Users\<USER>\Desktop\ai_gen\netty_proxy\netty_multiplex_proxy\proxy-server"/>
    <property name="os.arch" value="amd64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="sun.os.patch.level" value=""/>
    <property name="native.encoding" value="GBK"/>
    <property name="java.library.path" value="C:\Program Files\Java\graalvm-jdk-21.0.8+12.1\bin;C:\WINDOWS\Sun\Java\bin;C:\WINDOWS\system32;C:\WINDOWS;E:\Program Files (x86)\VMware\bin\;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\dotnet\;E:\nodejs22\;D:\xshell\;C:\Program Files\Java\graalvm-jdk-21.0.8+12.1\bin;D:\Git\cmd;F:\Windows Kits\10\Windows Performance Toolkit\;E:\maven-3.9.9\bin;D:\Lingma\bin;D:\golang\bin;D:\mingw64\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Roaming\npm;E:\Microsoft VS Code\bin;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Local\Programs\Ollama;D:\cursor\resources\app\bin;D:\Windsurf\bin;D:\Kiro\bin;D:\Comate\bin;C:\Users\<USER>\go\bin;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand;."/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="stderr.encoding" value="GBK"/>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="java.vm.version" value="21.0.8+12-LTS-jvmci-23.1-b72"/>
    <property name="sun.io.unicode.encoding" value="UnicodeLittle"/>
    <property name="java.class.version" value="65.0"/>
  </properties>
  <testcase name="testQueueDistribution" classname="com.xiang.proxy.server.core.ProxyProcessorQueueTest" time="0.285">
    <system-out><![CDATA[09:33:25.217 [main] INFO  c.x.proxy.server.core.ProxyProcessor - 自适应队列管理器已启用
09:33:25.220 [main] INFO  c.x.proxy.server.core.ProxyProcessor - ProxyProcessor初始化完成: ProxyProcessorConfig{queueCount=4, queueCapacity=1000, shutdownTimeoutSeconds=30, enableQueueMonitoring=true, workerThreadPrefix='proxy-worker-'}
09:33:25.221 [main] INFO  c.x.proxy.server.core.ProxyProcessor - 启动ProxyProcessor工作线程...
09:33:25.226 [main] INFO  c.x.p.s.core.AdaptiveQueueManager - 自适应队列管理器启动
09:33:25.226 [main] INFO  c.x.proxy.server.core.ProxyProcessor - 自适应队列管理器已启动
09:33:25.226 [main] INFO  c.x.proxy.server.core.ProxyProcessor - ProxyProcessor启动完成，工作线程数: 4
队列 0 请求数: 24
队列 1 请求数: 24
队列 2 请求数: 26
队列 3 请求数: 26
]]></system-out>
  </testcase>
  <testcase name="testQueueIndexConsistency" classname="com.xiang.proxy.server.core.ProxyProcessorQueueTest" time="0.003">
    <system-out><![CDATA[09:33:25.288 [main] INFO  c.x.proxy.server.core.ProxyProcessor - 自适应队列管理器已启用
09:33:25.288 [main] INFO  c.x.proxy.server.core.ProxyProcessor - ProxyProcessor初始化完成: ProxyProcessorConfig{queueCount=4, queueCapacity=1000, shutdownTimeoutSeconds=30, enableQueueMonitoring=true, workerThreadPrefix='proxy-worker-'}
09:33:25.288 [main] INFO  c.x.proxy.server.core.ProxyProcessor - 启动ProxyProcessor工作线程...
09:33:25.289 [main] INFO  c.x.p.s.core.AdaptiveQueueManager - 自适应队列管理器启动
09:33:25.289 [main] INFO  c.x.proxy.server.core.ProxyProcessor - 自适应队列管理器已启动
09:33:25.289 [main] INFO  c.x.proxy.server.core.ProxyProcessor - ProxyProcessor启动完成，工作线程数: 4
]]></system-out>
  </testcase>
  <testcase name="testConcurrentRequestProcessing" classname="com.xiang.proxy.server.core.ProxyProcessorQueueTest" time="0.013">
    <system-out><![CDATA[09:33:25.291 [main] INFO  c.x.proxy.server.core.ProxyProcessor - 自适应队列管理器已启用
09:33:25.291 [main] INFO  c.x.proxy.server.core.ProxyProcessor - ProxyProcessor初始化完成: ProxyProcessorConfig{queueCount=4, queueCapacity=1000, shutdownTimeoutSeconds=30, enableQueueMonitoring=true, workerThreadPrefix='proxy-worker-'}
09:33:25.291 [main] INFO  c.x.proxy.server.core.ProxyProcessor - 启动ProxyProcessor工作线程...
09:33:25.291 [main] INFO  c.x.p.s.core.AdaptiveQueueManager - 自适应队列管理器启动
09:33:25.291 [main] INFO  c.x.proxy.server.core.ProxyProcessor - 自适应队列管理器已启动
09:33:25.293 [main] INFO  c.x.proxy.server.core.ProxyProcessor - ProxyProcessor启动完成，工作线程数: 4
09:33:25.295 [proxy-worker-2] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 5573de0c-a63f-4379-93e7-f1689bf3e2d5 处理失败: Outbound handler not found: test-outbound
09:33:25.295 [proxy-worker-1] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 6ab64dde-d736-44d7-a591-8a5f23da4216 处理失败: Outbound handler not found: test-outbound
09:33:25.295 [proxy-worker-3] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 0f891b1e-2e34-42c4-9eb1-c9d20568b0ee 处理失败: Outbound handler not found: test-outbound
09:33:25.295 [proxy-worker-0] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 7065ceda-d264-4981-aeac-e37f6b27318d 处理失败: Outbound handler not found: test-outbound
09:33:25.297 [proxy-worker-3] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 1dfc3a8f-f670-4e21-bc34-61eed4b3339a 处理失败: Outbound handler not found: test-outbound
09:33:25.297 [proxy-worker-2] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 01ede827-8c2d-46da-be28-52c0b8ff51fd 处理失败: Outbound handler not found: test-outbound
09:33:25.297 [proxy-worker-0] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 a36ec5b8-910d-456b-bdaf-c23b15f57d26 处理失败: Outbound handler not found: test-outbound
09:33:25.297 [proxy-worker-3] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 4272cd49-0ce5-4c40-9732-35116647e31d 处理失败: Outbound handler not found: test-outbound
09:33:25.298 [proxy-worker-1] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 b6a23907-1ffb-4544-ad55-095436a396e1 处理失败: Outbound handler not found: test-outbound
09:33:25.298 [proxy-worker-2] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 8b647526-221e-4272-b964-b83960ac25f9 处理失败: Outbound handler not found: test-outbound
09:33:25.298 [proxy-worker-1] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 46f09d05-66d9-420a-a64a-25f5851b9521 处理失败: Outbound handler not found: test-outbound
09:33:25.298 [proxy-worker-3] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 87a9c344-ac8c-4c31-ac8a-59efd5347e79 处理失败: Outbound handler not found: test-outbound
09:33:25.298 [proxy-worker-0] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 ec6eaf31-dfb1-467c-a79f-3b875b555d60 处理失败: Outbound handler not found: test-outbound
09:33:25.298 [proxy-worker-2] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 54f9814b-c902-49df-9797-287b03a08f13 处理失败: Outbound handler not found: test-outbound
09:33:25.298 [proxy-worker-1] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 abe54d64-4f41-4042-95d3-596c106a2ca6 处理失败: Outbound handler not found: test-outbound
09:33:25.298 [proxy-worker-3] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 5dd53eff-d65a-4c78-b013-5a9d3eeeb20c 处理失败: Outbound handler not found: test-outbound
09:33:25.298 [proxy-worker-0] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 76863507-3960-4518-ab12-d55ee7afa3aa 处理失败: Outbound handler not found: test-outbound
09:33:25.298 [proxy-worker-2] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 590ad4c1-58fa-4bf1-84a5-098bbe1f5935 处理失败: Outbound handler not found: test-outbound
09:33:25.298 [proxy-worker-1] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 a1852b36-82ea-42fb-8164-8db3b285ade2 处理失败: Outbound handler not found: test-outbound
09:33:25.298 [proxy-worker-3] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 d15b83a1-4bfd-4244-97c4-01cdccf9ed6a 处理失败: Outbound handler not found: test-outbound
09:33:25.299 [proxy-worker-0] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 72b84747-0609-4a34-b14e-c839876c0d0b 处理失败: Outbound handler not found: test-outbound
09:33:25.299 [proxy-worker-2] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 8fdbdab1-ac3f-4009-9510-04ad09986256 处理失败: Outbound handler not found: test-outbound
09:33:25.299 [proxy-worker-1] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 c6380f03-d44e-44bc-8f33-a4038805467e 处理失败: Outbound handler not found: test-outbound
09:33:25.299 [proxy-worker-3] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 a8287430-99c4-40b3-a0ae-677747907e80 处理失败: Outbound handler not found: test-outbound
09:33:25.299 [proxy-worker-0] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 84ad6544-cfb2-4423-aca5-1d686de14a8d 处理失败: Outbound handler not found: test-outbound
09:33:25.299 [proxy-worker-1] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 1e0071a9-9a03-4fdd-8906-bf71fe4ce3e3 处理失败: Outbound handler not found: test-outbound
09:33:25.299 [proxy-worker-0] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 d926a46e-d313-4d05-a85d-b6313e287501 处理失败: Outbound handler not found: test-outbound
09:33:25.299 [proxy-worker-2] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 975f54a2-3333-48f5-8fa6-734ed7ea4ea2 处理失败: Outbound handler not found: test-outbound
09:33:25.299 [proxy-worker-3] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 2f3702c4-82ff-42de-9d4d-c9e7dc976809 处理失败: Outbound handler not found: test-outbound
09:33:25.299 [proxy-worker-1] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 f7677f4d-f4a8-4553-891c-98f5db20c0c4 处理失败: Outbound handler not found: test-outbound
09:33:25.299 [proxy-worker-0] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 e07f10db-3980-4583-bdff-59d2fe291b55 处理失败: Outbound handler not found: test-outbound
09:33:25.299 [proxy-worker-2] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 2ba0b092-875e-4970-8402-b160c6a79d84 处理失败: Outbound handler not found: test-outbound
09:33:25.299 [proxy-worker-3] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 8dd9c127-e81e-4d18-8102-6981f4cc5533 处理失败: Outbound handler not found: test-outbound
09:33:25.299 [proxy-worker-1] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 3c06dedf-8f09-4bb0-9153-0a535302ad03 处理失败: Outbound handler not found: test-outbound
09:33:25.299 [proxy-worker-0] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 d08fd85d-59cc-44ee-a557-b007f6b8a87f 处理失败: Outbound handler not found: test-outbound
09:33:25.299 [proxy-worker-2] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 288c149d-005b-4323-992f-d731bd17881f 处理失败: Outbound handler not found: test-outbound
09:33:25.300 [proxy-worker-3] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 5352b621-a4a1-48ef-8441-adebe01b37ad 处理失败: Outbound handler not found: test-outbound
09:33:25.300 [proxy-worker-1] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 580b75ed-6fad-4b7b-b023-a9d72c663cc8 处理失败: Outbound handler not found: test-outbound
09:33:25.300 [proxy-worker-0] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 34f763e6-b280-4c59-9271-d39cf3f68f39 处理失败: Outbound handler not found: test-outbound
09:33:25.300 [proxy-worker-3] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 2c773f96-77e3-42c5-8882-544fde54ea57 处理失败: Outbound handler not found: test-outbound
09:33:25.300 [proxy-worker-2] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 88ebc916-c69e-49c3-9e91-73f5a982f0a1 处理失败: Outbound handler not found: test-outbound
09:33:25.300 [proxy-worker-1] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 49fbd12a-cfa1-42b8-822e-da0560ab52f6 处理失败: Outbound handler not found: test-outbound
09:33:25.300 [proxy-worker-0] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 d99f66ff-4c76-4d83-82b2-611a3d5bcb84 处理失败: Outbound handler not found: test-outbound
09:33:25.300 [proxy-worker-2] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 f3569aed-0ec5-4970-920c-383d64b5df44 处理失败: Outbound handler not found: test-outbound
09:33:25.300 [proxy-worker-3] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 3d58363f-7df6-496c-8d9f-3522e81fdac3 处理失败: Outbound handler not found: test-outbound
09:33:25.300 [proxy-worker-1] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 a9522d16-87d7-45d1-aca8-ba0e5ce17e82 处理失败: Outbound handler not found: test-outbound
09:33:25.300 [proxy-worker-0] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 203099cf-7e59-495b-ab7f-135b48fa447a 处理失败: Outbound handler not found: test-outbound
09:33:25.301 [proxy-worker-2] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 2a8b0b12-b4a4-4022-ad76-a7610e36557f 处理失败: Outbound handler not found: test-outbound
09:33:25.301 [proxy-worker-3] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 93c2eb9b-c068-4d8f-bb4d-bc0662d89c28 处理失败: Outbound handler not found: test-outbound
09:33:25.301 [proxy-worker-1] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 8bae3b83-3318-4978-bcac-e0b872cead9a 处理失败: Outbound handler not found: test-outbound
09:33:25.301 [proxy-worker-0] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 402082a3-802e-4e01-b6c7-1a38cb00dd17 处理失败: Outbound handler not found: test-outbound
09:33:25.301 [proxy-worker-1] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 bd48a456-739d-4ff2-a82b-dea5c01747b9 处理失败: Outbound handler not found: test-outbound
09:33:25.301 [proxy-worker-3] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 1c7cdaae-fa8c-4473-ade8-a5f5c4ae8509 处理失败: Outbound handler not found: test-outbound
09:33:25.301 [proxy-worker-2] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 4a703a81-f5e5-4992-ae79-4b7a27119772 处理失败: Outbound handler not found: test-outbound
09:33:25.301 [proxy-worker-0] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 aa7cade7-256a-407e-bbd9-50c1048979ca 处理失败: Outbound handler not found: test-outbound
09:33:25.301 [proxy-worker-3] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 93468d65-3d85-40c5-bde2-020150d4f7e0 处理失败: Outbound handler not found: test-outbound
09:33:25.301 [proxy-worker-0] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 60833846-1442-4e3e-a2aa-0c2a6d0eeaca 处理失败: Outbound handler not found: test-outbound
09:33:25.301 [proxy-worker-1] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 afefb0c8-0388-4d63-ba3e-a4564c12bcb3 处理失败: Outbound handler not found: test-outbound
09:33:25.301 [proxy-worker-2] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 868e8d8b-1466-4a91-a9ea-9bfc3945f677 处理失败: Outbound handler not found: test-outbound
09:33:25.301 [proxy-worker-0] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 90b66e51-c34b-4d5a-8dac-e59e185c09b2 处理失败: Outbound handler not found: test-outbound
09:33:25.301 [proxy-worker-3] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 fdd9fdc6-f4a0-41af-b141-fbc73656f53d 处理失败: Outbound handler not found: test-outbound
09:33:25.301 [proxy-worker-2] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 d5e9d3c3-fb02-448b-b67d-cf9204c7b194 处理失败: Outbound handler not found: test-outbound
09:33:25.301 [proxy-worker-0] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 d7d9d815-5426-470c-b445-f2a5f287d2ae 处理失败: Outbound handler not found: test-outbound
09:33:25.301 [proxy-worker-1] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 f632d0e0-0c20-448b-8d06-fe97110345d4 处理失败: Outbound handler not found: test-outbound
09:33:25.301 [proxy-worker-3] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 3d876069-d90d-4890-9f10-d0e692cde301 处理失败: Outbound handler not found: test-outbound
09:33:25.301 [proxy-worker-2] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 565e5500-123c-41d3-8a65-9669bea4dcd9 处理失败: Outbound handler not found: test-outbound
09:33:25.301 [proxy-worker-0] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 97930295-0a80-4b12-b2d1-da10ca142877 处理失败: Outbound handler not found: test-outbound
09:33:25.301 [proxy-worker-1] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 5c728ee7-a2e5-4413-9380-816f42b3fc7a 处理失败: Outbound handler not found: test-outbound
09:33:25.301 [proxy-worker-0] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 4a705064-5fb9-4e2c-ad07-abb46611fc09 处理失败: Outbound handler not found: test-outbound
09:33:25.301 [proxy-worker-0] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 a5c7ff53-627d-4a20-97f6-8a4cd69fcc70 处理失败: Outbound handler not found: test-outbound
09:33:25.301 [proxy-worker-3] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 39d672d0-e0c3-48ca-961e-0c582f07dec4 处理失败: Outbound handler not found: test-outbound
09:33:25.301 [proxy-worker-2] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 dad3053d-92ed-4ae2-89cb-a3950217ac4d 处理失败: Outbound handler not found: test-outbound
09:33:25.301 [proxy-worker-1] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 beda57a4-30ca-4bae-9518-80379e96c55a 处理失败: Outbound handler not found: test-outbound
09:33:25.302 [proxy-worker-3] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 67bed4af-dbde-4b9d-9683-dd1e011fc17e 处理失败: Outbound handler not found: test-outbound
09:33:25.302 [proxy-worker-0] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 2ae63c70-9f9a-40d1-befb-32bf16004903 处理失败: Outbound handler not found: test-outbound
09:33:25.302 [proxy-worker-2] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 40b2b2d8-2478-42c4-987c-b3e3790891a5 处理失败: Outbound handler not found: test-outbound
09:33:25.302 [proxy-worker-1] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 d4055658-a310-4f3d-a6be-e5c58ff75ef7 处理失败: Outbound handler not found: test-outbound
09:33:25.302 [proxy-worker-3] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 80763d74-af1c-49dc-8807-e158298a5fae 处理失败: Outbound handler not found: test-outbound
09:33:25.302 [proxy-worker-2] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 3267fb8b-2d6f-4efa-a38e-48f0cbc75a5f 处理失败: Outbound handler not found: test-outbound
09:33:25.302 [proxy-worker-0] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 cda55d27-9b82-4a0f-9356-064d508e140d 处理失败: Outbound handler not found: test-outbound
09:33:25.302 [proxy-worker-1] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 49b2cac7-84af-4a2b-9172-39ed93552b9c 处理失败: Outbound handler not found: test-outbound
09:33:25.302 [proxy-worker-0] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 617906b9-d07a-4dff-8a8b-78af4e81bf69 处理失败: Outbound handler not found: test-outbound
09:33:25.302 [proxy-worker-2] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 82d15c0d-6f82-448a-ace9-4011605348e4 处理失败: Outbound handler not found: test-outbound
09:33:25.302 [proxy-worker-0] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 f268c701-2c14-4656-93c8-07522e49c6fc 处理失败: Outbound handler not found: test-outbound
09:33:25.302 [proxy-worker-1] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 8fea40a7-d5eb-4e47-bbf0-3ec9bab066f3 处理失败: Outbound handler not found: test-outbound
09:33:25.302 [proxy-worker-0] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 63be6155-85e7-4c9e-8890-b25a2d16b7ca 处理失败: Outbound handler not found: test-outbound
09:33:25.302 [proxy-worker-1] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 92949b26-e6a8-4c80-8882-db887c7a53c0 处理失败: Outbound handler not found: test-outbound
09:33:25.302 [proxy-worker-0] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 e3105ef0-c80c-42be-a718-59455884282d 处理失败: Outbound handler not found: test-outbound
09:33:25.302 [proxy-worker-0] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 99d29483-07cf-4dac-9b7e-d666d5069597 处理失败: Outbound handler not found: test-outbound
09:33:25.302 [proxy-worker-1] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 2e84694d-b7cb-47ad-bdb8-32b26e41d785 处理失败: Outbound handler not found: test-outbound
09:33:25.302 [proxy-worker-0] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 0a8fc5d2-80db-42e6-8ad0-861d70cc042e 处理失败: Outbound handler not found: test-outbound
09:33:25.302 [proxy-worker-0] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 d696c468-df62-48e8-8aff-7c5dabf2b642 处理失败: Outbound handler not found: test-outbound
09:33:25.302 [proxy-worker-1] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 06e24f98-d26a-49b0-a15b-eb2b8d41be15 处理失败: Outbound handler not found: test-outbound
09:33:25.302 [proxy-worker-0] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 8611c7e9-fb72-4892-a67a-d36572a0e593 处理失败: Outbound handler not found: test-outbound
09:33:25.302 [proxy-worker-1] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 e0513376-9a09-4745-acf7-732af733687e 处理失败: Outbound handler not found: test-outbound
09:33:25.303 [proxy-worker-1] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 b2e7ecd4-4279-4fca-952e-408bfa527772 处理失败: Outbound handler not found: test-outbound
09:33:25.303 [proxy-worker-1] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 7003efe9-1424-4817-8222-5a643ead45dd 处理失败: Outbound handler not found: test-outbound
09:33:25.303 [proxy-worker-1] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 f32f19f3-d7c8-4995-85fd-e3eae7b8d5fa 处理失败: Outbound handler not found: test-outbound
09:33:25.303 [proxy-worker-1] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 3c241d0e-38cd-4c9e-aea7-2ddfd84dec04 处理失败: Outbound handler not found: test-outbound
09:33:25.303 [proxy-worker-1] ERROR c.x.proxy.server.core.ProxyProcessor - 请求 018cbb89-a7ac-48f6-b929-1b9588f5df01 处理失败: Outbound handler not found: test-outbound
并发测试结果: 成功=0, 失败=100
]]></system-out>
  </testcase>
  <testcase name="testQueueIndexCalculation" classname="com.xiang.proxy.server.core.ProxyProcessorQueueTest" time="0.002">
    <system-out><![CDATA[09:33:25.305 [main] INFO  c.x.proxy.server.core.ProxyProcessor - 自适应队列管理器已启用
09:33:25.305 [main] INFO  c.x.proxy.server.core.ProxyProcessor - ProxyProcessor初始化完成: ProxyProcessorConfig{queueCount=4, queueCapacity=1000, shutdownTimeoutSeconds=30, enableQueueMonitoring=true, workerThreadPrefix='proxy-worker-'}
09:33:25.305 [main] INFO  c.x.proxy.server.core.ProxyProcessor - 启动ProxyProcessor工作线程...
09:33:25.306 [main] INFO  c.x.p.s.core.AdaptiveQueueManager - 自适应队列管理器启动
09:33:25.306 [main] INFO  c.x.proxy.server.core.ProxyProcessor - 自适应队列管理器已启动
09:33:25.306 [main] INFO  c.x.proxy.server.core.ProxyProcessor - ProxyProcessor启动完成，工作线程数: 4
]]></system-out>
  </testcase>
  <testcase name="testDifferentConnectionsRouteToDifferentQueues" classname="com.xiang.proxy.server.core.ProxyProcessorQueueTest" time="0.002">
    <system-out><![CDATA[09:33:25.308 [main] INFO  c.x.proxy.server.core.ProxyProcessor - 自适应队列管理器已启用
09:33:25.308 [main] INFO  c.x.proxy.server.core.ProxyProcessor - ProxyProcessor初始化完成: ProxyProcessorConfig{queueCount=4, queueCapacity=1000, shutdownTimeoutSeconds=30, enableQueueMonitoring=true, workerThreadPrefix='proxy-worker-'}
09:33:25.308 [main] INFO  c.x.proxy.server.core.ProxyProcessor - 启动ProxyProcessor工作线程...
09:33:25.308 [main] INFO  c.x.p.s.core.AdaptiveQueueManager - 自适应队列管理器启动
09:33:25.309 [main] INFO  c.x.proxy.server.core.ProxyProcessor - 自适应队列管理器已启动
09:33:25.309 [main] INFO  c.x.proxy.server.core.ProxyProcessor - ProxyProcessor启动完成，工作线程数: 4
]]></system-out>
  </testcase>
</testsuite>