package com.xiang.proxy.server.outbound;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 连接状态管理器
 * 提供连接状态监控、统计和清理功能
 */
public class ConnectionStateManager {
    private static final Logger logger = LoggerFactory.getLogger(ConnectionStateManager.class);
    
    private final ConcurrentMap<String, OutboundConnection> connections = new ConcurrentHashMap<>();
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(2);
    
    // 状态统计
    private final AtomicLong creatingCount = new AtomicLong(0);
    private final AtomicLong connectedCount = new AtomicLong(0);
    private final AtomicLong closingCount = new AtomicLong(0);
    private final AtomicLong closedCount = new AtomicLong(0);
    private final AtomicLong failedCount = new AtomicLong(0);
    private final AtomicLong errorCount = new AtomicLong(0);
    
    // 配置参数
    private final long connectionTimeout = 30000; // 30秒连接超时
    private final long cleanupInterval = 60000;   // 60秒清理间隔
    
    public ConnectionStateManager() {
        startPeriodicTasks();
    }
    
    /**
     * 注册连接
     */
    public void registerConnection(OutboundConnection connection) {
        if (connection == null) {
            return;
        }
        
        String connectionId = connection.getConnectionId();
        connections.put(connectionId, connection);
        updateStateCount(connection.getConnectionState(), 1);
        
        logger.debug("注册连接: connectionId={}, state={}, total={}", 
                    connectionId, connection.getConnectionState(), connections.size());
    }
    
    /**
     * 注销连接
     */
    public void unregisterConnection(String connectionId) {
        OutboundConnection connection = connections.remove(connectionId);
        if (connection != null) {
            updateStateCount(connection.getConnectionState(), -1);
            logger.debug("注销连接: connectionId={}, state={}, total={}", 
                        connectionId, connection.getConnectionState(), connections.size());
        }
    }
    
    /**
     * 获取连接
     */
    public OutboundConnection getConnection(String connectionId) {
        return connections.get(connectionId);
    }
    
    /**
     * 检查连接是否可复用
     */
    public boolean isConnectionReusable(String connectionId) {
        OutboundConnection connection = connections.get(connectionId);
        if (connection == null) {
            return false;
        }
        
        // 检查连接状态
        if (!connection.isReusable()) {
            logger.debug("连接不可复用: connectionId={}, state={}", 
                        connectionId, connection.getConnectionState());
            return false;
        }
        
        // 检查连接是否超时
        long stateAge = System.currentTimeMillis() - connection.getStateChangeTime();
        if (connection.isCreating() && stateAge > connectionTimeout) {
            logger.warn("连接创建超时: connectionId={}, age={}ms", connectionId, stateAge);
            connection.markFailed();
            return false;
        }
        
        return true;
    }
    
    /**
     * 获取状态统计信息
     */
    public ConnectionStateStats getStats() {
        return new ConnectionStateStats(
                creatingCount.get(),
                connectedCount.get(),
                closingCount.get(),
                closedCount.get(),
                failedCount.get(),
                errorCount.get(),
                connections.size()
        );
    }
    
    /**
     * 启动定期任务
     */
    private void startPeriodicTasks() {
        // 连接超时检查任务
        scheduler.scheduleWithFixedDelay(this::checkConnectionTimeouts, 
                                       connectionTimeout / 2, connectionTimeout / 2, TimeUnit.MILLISECONDS);
        
        // 连接清理任务
        scheduler.scheduleWithFixedDelay(this::cleanupTerminatedConnections, 
                                       cleanupInterval, cleanupInterval, TimeUnit.MILLISECONDS);
        
        // 状态统计任务
        scheduler.scheduleWithFixedDelay(this::logConnectionStats, 
                                       60000, 60000, TimeUnit.MILLISECONDS);
    }
    
    /**
     * 检查连接超时
     */
    private void checkConnectionTimeouts() {
        long currentTime = System.currentTimeMillis();
        int timeoutCount = 0;
        
        for (OutboundConnection connection : connections.values()) {
            if (connection.isCreating()) {
                long stateAge = currentTime - connection.getStateChangeTime();
                if (stateAge > connectionTimeout) {
                    logger.warn("连接创建超时，标记为失败: connectionId={}, age={}ms", 
                               connection.getConnectionId(), stateAge);
                    connection.markFailed();
                    timeoutCount++;
                }
            }
        }
        
        if (timeoutCount > 0) {
            logger.info("检查连接超时完成，处理超时连接: {}", timeoutCount);
        }
    }
    
    /**
     * 清理已终止的连接
     */
    private void cleanupTerminatedConnections() {
        long currentTime = System.currentTimeMillis();
        int cleanupCount = 0;
        
        connections.entrySet().removeIf(entry -> {
            OutboundConnection connection = entry.getValue();
            if (connection.isTerminated()) {
                long stateAge = currentTime - connection.getStateChangeTime();
                // 终态连接保留5分钟后清理
                if (stateAge > 300000) {
                    updateStateCount(connection.getConnectionState(), -1);
                    logger.debug("清理终态连接: connectionId={}, state={}, age={}ms", 
                                connection.getConnectionId(), connection.getConnectionState(), stateAge);
                    return true;
                }
            }
            return false;
        });
        
        if (cleanupCount > 0) {
            logger.info("清理终态连接完成，清理数量: {}", cleanupCount);
        }
    }
    
    /**
     * 记录连接统计信息
     */
    private void logConnectionStats() {
        ConnectionStateStats stats = getStats();
        logger.info("连接状态统计: creating={}, connected={}, closing={}, closed={}, failed={}, error={}, total={}", 
                   stats.creatingCount, stats.connectedCount, stats.closingCount, 
                   stats.closedCount, stats.failedCount, stats.errorCount, stats.totalCount);
    }
    
    /**
     * 更新状态计数
     */
    private void updateStateCount(ConnectionState state, long delta) {
        switch (state) {
            case CREATING:
                creatingCount.addAndGet(delta);
                break;
            case CONNECTED:
                connectedCount.addAndGet(delta);
                break;
            case CLOSING:
                closingCount.addAndGet(delta);
                break;
            case CLOSED:
                closedCount.addAndGet(delta);
                break;
            case FAILED:
                failedCount.addAndGet(delta);
                break;
            case ERROR:
                errorCount.addAndGet(delta);
                break;
        }
    }
    
    /**
     * 关闭管理器
     */
    public void shutdown() {
        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }
        
        connections.clear();
        logger.info("连接状态管理器已关闭");
    }
    
    /**
     * 连接状态统计信息
     */
    public static class ConnectionStateStats {
        public final long creatingCount;
        public final long connectedCount;
        public final long closingCount;
        public final long closedCount;
        public final long failedCount;
        public final long errorCount;
        public final long totalCount;
        
        public ConnectionStateStats(long creatingCount, long connectedCount, long closingCount,
                                  long closedCount, long failedCount, long errorCount, long totalCount) {
            this.creatingCount = creatingCount;
            this.connectedCount = connectedCount;
            this.closingCount = closingCount;
            this.closedCount = closedCount;
            this.failedCount = failedCount;
            this.errorCount = errorCount;
            this.totalCount = totalCount;
        }
        
        @Override
        public String toString() {
            return String.format("ConnectionStateStats{creating=%d, connected=%d, closing=%d, closed=%d, failed=%d, error=%d, total=%d}",
                               creatingCount, connectedCount, closingCount, closedCount, failedCount, errorCount, totalCount);
        }
    }
}
